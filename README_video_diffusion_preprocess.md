# Video Diffusion Model Integration for RDT Preprocessing

这个文档描述了如何使用video diffusion model的VAE架构和transformer blocks来处理RDT数据的预处理。

## 功能概述

修改后的`preprocess_rdt.py`现在支持两种模式：

1. **原始模式**: 使用dummy模型进行简单的特征提取
2. **Video Diffusion模式**: 使用video diffusion model的VAE和Guidance类进行高级特征提取

## 主要改进

### 1. Video Diffusion Model集成
- 集成了`AutoencoderKLCausal3D` VAE模型
- 集成了`HunyuanVideoTransformer3DModelPacked` transformer模型
- 使用`Guidance`类包装transformer并提取中间特征

### 2. 整个Episode处理
- 将整个episode的图像作为video序列处理
- 通过VAE encoder得到latent representations
- 对latent进行分段切片处理

### 3. Transformer Block特征提取
- 使用Guidance类的`load_features`方法
- 从指定的transformer blocks中提取motion features
- 支持MOFT (Motion-Oriented Feature Transform) 方法

## 使用方法

### 基本用法 (Dummy模型)
```bash
python dataset/preprocess_rdt.py task_name camera_type num_episodes --T 9
```

### Video Diffusion模式 (不使用text encoder)
```bash
python dataset/preprocess_rdt.py task_name camera_type num_episodes \
    --T 9 \
    --use_video_diffusion \
    --vae_path /path/to/vae/model \
    --transformer_path /path/to/transformer/model \
    --chunk_size 8 \
    --latent_slice_size 16
```

### Video Diffusion模式 + Hunyuan Text Encoder
```bash
python dataset/preprocess_rdt.py task_name camera_type num_episodes \
    --T 9 \
    --use_video_diffusion \
    --vae_path /path/to/vae/model \
    --transformer_path /path/to/transformer/model \
    --text_encoder_path /path/to/text/encoder \
    --chunk_size 8 \
    --latent_slice_size 16
```

## 参数说明

- `--use_video_diffusion`: 启用video diffusion模式
- `--vae_path`: VAE模型路径
- `--transformer_path`: Transformer模型路径
- `--text_encoder_path`: Text encoder模型路径 (可选，用于真实的文本编码)
- `--chunk_size`: VAE处理的chunk大小 (默认: 8)
- `--latent_slice_size`: Latent切片大小 (默认: 16)

## 技术细节

### VAE处理流程
1. 加载整个episode的图像
2. 转换为video tensor格式: `(B, C, T, H, W)`
3. 使用 `hunyuan.vae_encode` 进行编码，与framepack保持一致
4. 确保所有操作在CUDA上运行，优化性能
5. 编码后移到CPU节省内存
6. 得到latent: `(B, latent_channels, T', H', W')`，包含scaling factor

### Text Encoder处理 (如果启用)
1. 使用 `framepack_utils.load_text_encoder1` 和 `load_text_encoder2` 加载模型
2. 创建args对象，传递正确的text_encoder1和text_encoder2路径
3. 使用 `hunyuan.encode_prompt_conds` 进行编码，完全模仿framepack方式
4. 使用 `crop_or_pad_yield_mask` 处理attention mask
5. 将embeddings移到CPU节省内存
6. 如果text encoder不可用，回退到虚拟embeddings

### Transformer特征提取
1. 创建Guidance模型实例
2. 对latent进行分段切片，确保在CUDA上
3. 确保所有tensor (hidden_states, encoder_hidden_states, pooled_projections) 在CUDA上
4. 使用真实或虚拟的text embeddings
5. 通过transformer进行前向传播，使用autocast优化
6. 使用`load_features`方法提取指定block的特征
7. 对特征进行池化和组合，确保在CUDA上运行

### Guidance类配置
- 默认使用block 19进行特征提取
- 支持motion channel分析 (MOFT)
- 可配置latent尺寸和patch size

## 输出格式

### Pre-latent特征
处理后的特征保存为HDF5格式：
- 文件名: `episode_{i}_prelatent.hdf5`
- 数据集名: `cam_high_pre_features`
- 形状: `(num_windows, feature_dim)`

### Text Embeddings (如果启用text encoder)
文本嵌入保存为HDF5格式：
- 文件名: `episode_{i}_text_embeddings.hdf5`
- 数据集:
  - `llama_vec`: LLM编码的文本特征 `(1, 512, 4096)`
  - `llama_attention_mask`: 注意力掩码 `(1, 512)`
  - `clip_l_pooler`: CLIP编码的池化特征 `(1, 768)`
  - `instruction`: 原始指令文本 (UTF-8编码)

## 测试

运行测试脚本验证功能：
```bash
python test_preprocess_rdt.py
```

测试包括：
1. Dummy模型基本功能测试
2. Video diffusion模型可用性测试
3. Guidance类集成测试

## 注意事项

1. **内存需求**: Video diffusion模式需要更多GPU内存
2. **模型路径**: 确保VAE和transformer模型路径正确
3. **依赖项**: 需要安装相关的video diffusion模型依赖
4. **设备兼容性**: 建议使用CUDA设备以获得最佳性能

## 故障排除

### 常见问题

1. **ImportError**: Video diffusion models not available
   - 检查模型依赖是否正确安装
   - 确认模型文件路径是否存在

2. **CUDA out of memory**
   - 减少batch size或chunk size
   - 使用CPU模式 (性能较慢)

3. **模型加载失败**
   - 检查模型文件格式和版本兼容性
   - 确认设备内存充足

## 扩展功能

可以通过以下方式扩展功能：

1. **自定义Guidance配置**: 修改guidance_blocks列表
2. **添加更多特征提取方法**: 在`process_latent_through_transformer`中实现
3. **支持不同的VAE模型**: 在`load_video_diffusion_models`中添加
4. **优化内存使用**: 实现更高效的批处理策略
