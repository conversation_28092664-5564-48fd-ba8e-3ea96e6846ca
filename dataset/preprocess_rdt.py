import sys
sys.path.append('/media/jiayueru/Codes/RoboTwin/policy/RDT')

import os
import numpy as np
import pickle
import cv2
import argparse
from PIL import Image
import torch
import torch.nn.functional as F
import h5py
import json
from typing import Optional, Union

# 导入video diffusion model相关模块
try:
    from hunyuan_model.autoencoder_kl_causal_3d import AutoencoderKLCausal3D
    from frame_pack.hunyuan_video_packed import HunyuanVideoTransformer3DModelPacked, load_packed_model
    from frame_pack.framepack_utils import load_vae as load_framepack_vae
    from hunyuan_model.vae import load_vae as load_hunyuan_vae
    from frame_pack.guidance import Guidance
    from hunyuan_model.text_encoder import TextEncoder, load_text_encoder_1, load_text_encoder_2, PROMPT_TEMPLATE
    VIDEO_DIFFUSION_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Video diffusion models not available: {e}")
    VIDEO_DIFFUSION_AVAILABLE = False

# 处理pre-latent特征提取和保存
#
# 使用方法:
# 1. 使用dummy模型 (原始方法):
#    python preprocess_rdt.py task_name camera_type num_episodes --T 9
#
# 2. 使用video diffusion模型 (不使用text encoder):
#    python preprocess_rdt.py task_name camera_type num_episodes --T 9 \
#           --use_video_diffusion \
#           --vae_path /path/to/vae/model \
#           --transformer_path /path/to/transformer/model \
#           --chunk_size 8 --latent_slice_size 16
#
# 3. 使用video diffusion模型 + hunyuan text encoder:
#    python preprocess_rdt.py task_name camera_type num_episodes --T 9 \
#           --use_video_diffusion \
#           --vae_path /path/to/vae/model \
#           --transformer_path /path/to/transformer/model \
#           --text_encoder_path /path/to/text/encoder \
#           --chunk_size 8 --latent_slice_size 16

def extract_pre_latent_features(subfolder_path, T, pre_latent_model, preprocess, device, instruction, episode_idx=0):
    pkl_files = [f for f in os.listdir(subfolder_path) if f.endswith('.pkl')]
    pkl_files = sorted(pkl_files, key=lambda x: int(x.split('.')[0]))
    cam_high = []
    for j in range(len(pkl_files)):
        pkl_file_path = os.path.join(subfolder_path, f'{j}.pkl')
        with open(pkl_file_path, 'rb') as pkl_f:
            data = pickle.load(pkl_f)
        camera_high = data['observation']['head_camera']['rgb']
        camera_high = camera_high[:,:,::-1]
        camera_high_resized = cv2.resize(camera_high, (640,480))
        cam_high.append(camera_high_resized)
    cam_high_pre_features = []
    for j in range(0, len(pkl_files) - T + 1):
        print(f"[Episode {episode_idx}] Extracting pre-latent feature for step t={j} ~ t+T-1={j+T-1}")
        imgs = [cam_high[j + k] for k in range(T)]
        imgs_pil = [Image.fromarray(img) for img in imgs]
        images = torch.stack([preprocess(img_pil) for img_pil in imgs_pil]).to(device)  # (T, C, H, W)
        with torch.no_grad():
            latent = pre_latent_model.encode_latent(images, instruction)
        cam_high_pre_features.append(latent.cpu().numpy())
    return np.array(cam_high_pre_features)


def extract_pre_latent_features_with_video_diffusion(
    subfolder_path,
    T,
    vae_model,
    guidance_model,
    preprocess,
    device,
    instruction,
    episode_idx=0,
    chunk_size=8,
    latent_slice_size=16,
    text_encoder_1=None,
    text_encoder_2=None,
    tokenizer_1=None,
    tokenizer_2=None
):
    """
    使用video diffusion model的VAE和Guidance处理整个episode的图像

    Args:
        subfolder_path: episode数据路径
        T: 时间窗口大小
        vae_model: video VAE模型
        guidance_model: Guidance模型实例
        preprocess: 图像预处理函数
        device: 设备
        instruction: 指令文本
        episode_idx: episode索引
        chunk_size: VAE处理的chunk大小
        latent_slice_size: latent切片大小
    """
    if not VIDEO_DIFFUSION_AVAILABLE:
        raise ImportError("Video diffusion models are not available. Please check your installation.")

    pkl_files = [f for f in os.listdir(subfolder_path) if f.endswith('.pkl')]
    pkl_files = sorted(pkl_files, key=lambda x: int(x.split('.')[0]))

    # 加载整个episode的图像
    cam_high = []
    for j in range(len(pkl_files)):
        pkl_file_path = os.path.join(subfolder_path, f'{j}.pkl')
        with open(pkl_file_path, 'rb') as pkl_f:
            data = pickle.load(pkl_f)
        camera_high = data['observation']['head_camera']['rgb']
        camera_high = camera_high[:,:,::-1]  # BGR to RGB
        camera_high_resized = cv2.resize(camera_high, (640, 480))
        cam_high.append(camera_high_resized)

    print(f"[Episode {episode_idx}] Processing {len(cam_high)} frames through video diffusion model")

    # 将图像转换为video tensor格式: (B, C, T, H, W)
    episode_images = []
    for img in cam_high:
        img_pil = Image.fromarray(img)
        img_tensor = preprocess(img_pil)
        episode_images.append(img_tensor)

    # Stack成video tensor: (C, T, H, W)
    video_tensor = torch.stack(episode_images, dim=1)  # (C, T, H, W)
    video_tensor = video_tensor.unsqueeze(0)  # (1, C, T, H, W)
    video_tensor = video_tensor.to(device)

    # 通过VAE encoder得到latent representations，使用hunyuan.vae_encode
    print(f"[Episode {episode_idx}] Encoding video through VAE...")

    # 导入hunyuan模块
    from frame_pack import hunyuan

    # 确保VAE在CUDA上，并设置正确的dtype
    vae_model.to(device)

    with torch.no_grad():
        # 将video tensor转换为VAE期望的格式 [-1, 1]，并确保在CUDA上
        video_tensor_normalized = video_tensor * 2.0 - 1.0
        video_tensor_normalized = video_tensor_normalized.to(device=device, dtype=vae_model.dtype)

        # 使用hunyuan.vae_encode进行编码，与framepack保持一致
        video_latents = hunyuan.vae_encode(video_tensor_normalized, vae_model)  # 包含scaling factor

        # 移到CPU以节省内存，与framepack保持一致
        video_latents = video_latents.cpu()

    print(f"[Episode {episode_idx}] Video latents shape: {video_latents.shape}")

    # 对latent进行分段切片处理
    cam_high_pre_features = []
    B, C, T_latent, H_latent, W_latent = video_latents.shape

    # 计算可以提取的时间窗口数量（基于latent时间维度）
    # 通常VAE的时间压缩比例是4:1
    latent_T_window = max(1, T // 4)  # 确保至少有1帧

    # 将VAE移回CPU以节省内存，与framepack保持一致
    vae_model.to("cpu")

    for j in range(0, max(1, T_latent - latent_T_window + 1)):
        print(f"[Episode {episode_idx}] Processing latent slice t={j} ~ t+{latent_T_window-1}={j+latent_T_window-1}")

        # 提取时间窗口的latent slice，并移到CUDA
        latent_slice = video_latents[:, :, j:j+latent_T_window, :, :]  # (1, C, latent_T_window, H', W')
        latent_slice = latent_slice.to(device)  # 确保在CUDA上

        # 通过transformer block处理latent features
        with torch.no_grad():
            # 这里需要根据具体的transformer模型接口调整
            # 通常需要timestep, text embeddings等额外输入
            processed_latent = process_latent_through_transformer(
                latent_slice, guidance_model, instruction, device, text_encoder_1, text_encoder_2, tokenizer_1, tokenizer_2
            )

        cam_high_pre_features.append(processed_latent.cpu().numpy())

    return np.array(cam_high_pre_features)


def encode_instruction_with_hunyuan_text_encoder(instruction, text_encoder_1, text_encoder_2, tokenizer_1, tokenizer_2, device, custom_system_prompt=None):
    """
    使用hunyuan video的text encoder处理指令文本，模仿fpack_generate_video.py的处理方式

    Args:
        instruction: 指令文本
        text_encoder_1: 第一个text encoder (LLM)
        text_encoder_2: 第二个text encoder (CLIP)
        tokenizer_1: 第一个tokenizer (LlamaTokenizerFast)
        tokenizer_2: 第二个tokenizer (CLIPTokenizer)
        device: 设备
        custom_system_prompt: 自定义系统提示 (可选)

    Returns:
        llama_vec, llama_attention_mask, clip_l_pooler
    """
    # 导入必要的函数
    try:
        from hunyuan_model import hunyuan
        from hunyuan_model.text_encoder import crop_or_pad_yield_mask
    except ImportError as e:
        print(f"Warning: Failed to import hunyuan functions: {e}")
        return None, None, None

    # 使用与fpack_generate_video.py相同的编码方式
    with torch.autocast(device_type=device.type if hasattr(device, 'type') else 'cuda', dtype=text_encoder_1.dtype), torch.no_grad():
        llama_vec, clip_l_pooler = hunyuan.encode_prompt_conds(
            instruction, text_encoder_1, text_encoder_2, tokenizer_1, tokenizer_2,
            custom_system_prompt=custom_system_prompt
        )

        # 移到CPU以节省内存
        llama_vec = llama_vec.cpu()
        clip_l_pooler = clip_l_pooler.cpu()

        # 处理attention mask，与原代码保持一致
        llama_vec, llama_attention_mask = crop_or_pad_yield_mask(llama_vec, length=512)

    return llama_vec, llama_attention_mask, clip_l_pooler


def create_dummy_text_embeddings(batch_size, device, dtype):
    """
    创建虚拟的text embeddings

    Args:
        batch_size: batch大小
        device: 设备
        dtype: 数据类型

    Returns:
        encoder_hidden_states, attention_mask, pooled_projections
    """
    seq_len = 256  # 文本序列长度
    text_dim = 4096  # 文本嵌入维度
    pooled_dim = 768  # pooled projection维度

    # 创建虚拟的encoder hidden states (text embeddings)
    encoder_hidden_states = torch.randn(batch_size, seq_len, text_dim, device=device, dtype=dtype)

    # 创建虚拟的attention mask
    attention_mask = torch.ones(batch_size, seq_len, device=device, dtype=torch.bool)

    # 创建虚拟的pooled projections
    pooled_projections = torch.randn(batch_size, pooled_dim, device=device, dtype=dtype)

    return encoder_hidden_states, attention_mask, pooled_projections


def process_latent_through_transformer(latent_slice, guidance_model, instruction, device, text_encoder_1=None, text_encoder_2=None, tokenizer_1=None, tokenizer_2=None):
    """
    通过transformer block处理latent features，使用Guidance类提取特征

    Args:
        latent_slice: latent特征切片 (B, C, T, H, W)
        guidance_model: Guidance模型实例
        instruction: 指令文本
        device: 设备
        text_encoder_1: 第一个text encoder (可选)
        text_encoder_2: 第二个text encoder (可选)
        tokenizer_1: 第一个tokenizer (可选)
        tokenizer_2: 第二个tokenizer (可选)

    Returns:
        processed_latent: 处理后的latent特征
    """
    B, C, T, H, W = latent_slice.shape

    # 如果提供了text encoder和tokenizer，使用真实的text encoding
    if (text_encoder_1 is not None and text_encoder_2 is not None and
        tokenizer_1 is not None and tokenizer_2 is not None):
            llama_vec, llama_attention_mask, clip_l_pooler = encode_instruction_with_hunyuan_text_encoder(
                instruction, text_encoder_1, text_encoder_2, tokenizer_1, tokenizer_2, device
            )

            # 移到设备并确保batch size匹配
            if llama_vec is not None:
                llama_vec = llama_vec.to(device, dtype=guidance_model.dtype)
                if llama_vec.shape[0] != B:
                    llama_vec = llama_vec.repeat(B, 1, 1)

            if llama_attention_mask is not None:
                llama_attention_mask = llama_attention_mask.to(device)
                if llama_attention_mask.shape[0] != B:
                    llama_attention_mask = llama_attention_mask.repeat(B, 1)

            if clip_l_pooler is not None:
                clip_l_pooler = clip_l_pooler.to(device, dtype=guidance_model.dtype)
                if clip_l_pooler.shape[0] != B:
                    clip_l_pooler = clip_l_pooler.repeat(B, 1)

            encoder_hidden_states = llama_vec
            attention_mask = llama_attention_mask
            pooled_projections = clip_l_pooler

    # 创建虚拟的timestep和guidance
    timestep = torch.tensor([0] * B, device=device, dtype=guidance_model.dtype)
    guidance = torch.tensor([6000.0] * B, device=device, dtype=guidance_model.dtype)

    # 将latent_slice转换为transformer期望的格式
    hidden_states = latent_slice.to(device=device, dtype=guidance_model.dtype)

        # 通过transformer进行前向传播
    with torch.autocast(device_type="cuda", dtype=guidance_model.dtype):
        _ = guidance_model.transformer(
            hidden_states=hidden_states,
            encoder_hidden_states=encoder_hidden_states,
            encoder_attention_mask=attention_mask, 
            pooled_projections=pooled_projections,
            timestep=timestep,
            guidance=guidance,
            return_dict=False,
        )

    # 使用Guidance类的load_features方法提取特征
    motion_features = guidance_model.load_features(moft=True)

    # 将提取的特征组合成单一的特征向量
    combined_features = []
    for block_name, features in motion_features.items():
        # features shape: (C, T, H, W)
        # 对特征进行全局平均池化，确保在CUDA上
        features = features.to(device)
        pooled = F.adaptive_avg_pool3d(features.unsqueeze(0), (1, 1, 1))  # (1, C, 1, 1, 1)
        pooled = pooled.squeeze()  # (C,)
        combined_features.append(pooled)

    if combined_features:
        # 将所有block的特征连接起来
        processed_latent = torch.cat(combined_features, dim=0).unsqueeze(0)  # (1, total_features)
    else:
        # 如果没有提取到特征，使用简化的池化方法
        processed_latent = F.adaptive_avg_pool3d(latent_slice, (1, 1, 1)).squeeze()
        if processed_latent.dim() == 1:
            processed_latent = processed_latent.unsqueeze(0)


    return processed_latent


def load_video_diffusion_models(vae_path, transformer_path, device, text_encoder_path=None):
    """
    加载video diffusion模型，模仿fpack_generate_video.py的加载方式

    Args:
        vae_path: VAE模型路径
        transformer_path: transformer模型路径
        device: 设备
        text_encoder_path: text encoder模型路径 (可选)

    Returns:
        vae_model, guidance_model, text_encoder_1, text_encoder_2, tokenizer_1, tokenizer_2
    """
    if not VIDEO_DIFFUSION_AVAILABLE:
        raise ImportError("Video diffusion models are not available. Please check your installation.")

    print(f"Loading VAE model from {vae_path}")
    # 加载VAE模型，与framepack保持一致
    vae_model = load_framepack_vae(vae_path, None, None, device)
    vae_model.eval()

    print(f"Loading transformer model from {transformer_path}")
    # 加载transformer模型，与framepack保持一致
    transformer_model = load_packed_model(device, transformer_path, "sdpa", "cpu")
    transformer_model.eval()

    print("Creating Guidance model...")
    # 创建Guidance模型实例
    guidance_model = Guidance(transformer_model)

    # 初始化text encoder变量
    text_encoder_1 = None
    text_encoder_2 = None
    tokenizer_1 = None
    tokenizer_2 = None

    if text_encoder_path is not None:
        try:
            print(f"Loading text encoders and tokenizers from {text_encoder_path}")

            # 模仿fpack_generate_video.py的加载方式
            from frame_pack.framepack_utils import load_text_encoder1, load_text_encoder2
            from types import SimpleNamespace

            # 创建args对象，模仿选中代码的方式
            args = SimpleNamespace()
            args.text_encoder1 = text_encoder_path  # 基础路径，函数内部会处理子目录
            args.text_encoder2 = text_encoder_path  # 基础路径，函数内部会处理子目录

            # 加载text encoders，使用framepack_utils中的函数
            print(f"Loading text_encoder1 from: {args.text_encoder1}")
            tokenizer_1, text_encoder_1 = load_text_encoder1(args, fp8_llm=False, device=device)

            print(f"Loading text_encoder2 from: {args.text_encoder2}")
            tokenizer_2, text_encoder_2 = load_text_encoder2(args)
            text_encoder_2.to(device)

            print("Text encoders and tokenizers loaded successfully")
        except Exception as e:
            print(f"Warning: Failed to load text encoders: {e}")
            print("Will use dummy text embeddings instead")
            import traceback
            traceback.print_exc()

    return vae_model, guidance_model, text_encoder_1, text_encoder_2, tokenizer_1, tokenizer_2


def save_text_embeddings_for_episode(instruction, text_encoder_1, text_encoder_2, tokenizer_1, tokenizer_2, device, save_path, episode_idx):
    """
    为每个episode保存text embeddings，模仿fpack_generate_video.py的处理方式

    Args:
        instruction: 指令文本
        text_encoder_1: 第一个text encoder
        text_encoder_2: 第二个text encoder
        tokenizer_1: 第一个tokenizer
        tokenizer_2: 第二个tokenizer
        device: 设备
        save_path: 保存路径
        episode_idx: episode索引
    """
    if (text_encoder_1 is None or text_encoder_2 is None or
        tokenizer_1 is None or tokenizer_2 is None):
        print(f"[Episode {episode_idx}] Text encoders not available, skipping text embedding save")
        return

    try:
        # 使用与fpack_generate_video.py相同的编码方式
        llama_vec, llama_attention_mask, clip_l_pooler = encode_instruction_with_hunyuan_text_encoder(
            instruction, text_encoder_1, text_encoder_2, tokenizer_1, tokenizer_2, device
        )

        if llama_vec is not None and clip_l_pooler is not None:
            # 保存text embeddings到HDF5文件
            text_embeddings_path = os.path.join(save_path, f'episode_{episode_idx}_text_embeddings.hdf5')
            with h5py.File(text_embeddings_path, 'w') as f:
                # 保存llama embeddings
                f.create_dataset('llama_vec', data=llama_vec.cpu().numpy())
                if llama_attention_mask is not None:
                    f.create_dataset('llama_attention_mask', data=llama_attention_mask.cpu().numpy())

                # 保存clip embeddings
                f.create_dataset('clip_l_pooler', data=clip_l_pooler.cpu().numpy())

                # 保存原始指令文本
                f.create_dataset('instruction', data=instruction.encode('utf-8'))

            print(f"[Episode {episode_idx}] Saved text embeddings to {text_embeddings_path}")
        else:
            print(f"[Episode {episode_idx}] Failed to generate text embeddings")

    except Exception as e:
        print(f"[Episode {episode_idx}] Error saving text embeddings: {e}")


class DummyPreLatentModel:
    def __init__(self, feature_dim=512):
        self.feature_dim = feature_dim
    def encode_latent(self, images, text):
        # images: (batch, C, H, W), text: str
        # 返回随机特征，shape=(batch, feature_dim)
        batch = images.shape[0]
        return torch.randn(batch, self.feature_dim)

def main():
    parser = argparse.ArgumentParser(description='Extract pre-latent features using video diffusion models.')
    parser.add_argument('task_name', type=str, default='block_handover', help='The name of the task (e.g., block_handover)')
    parser.add_argument('head_camera_type', type=str, default='D435', help='camera type')
    parser.add_argument('expert_data_num', type=int, default=100, help='Number of episodes to process (e.g., 50)')
    parser.add_argument('--T', type=int, default=9, help='Time interval for pre-latent feature extraction')
    parser.add_argument('--use_video_diffusion', action='store_true', help='Use video diffusion model for feature extraction')
    parser.add_argument('--vae_path', type=str, default=None, help='Path to VAE model')
    parser.add_argument('--transformer_path', type=str, default=None, help='Path to transformer model')
    parser.add_argument('--text_encoder1', type=str, default=None, help='Path to text encoder 1 model')
    parser.add_argument('--text_encoder2', type=str, default=None, help='Path to text encoder 2 model')
    parser.add_argument('--chunk_size', type=int, default=8, help='Chunk size for VAE processing')
    parser.add_argument('--latent_slice_size', type=int, default=16, help='Latent slice size for transformer processing')
    args = parser.parse_args()

    task_name = args.task_name
    head_camera_type = args.head_camera_type
    num = args.expert_data_num
    T = args.T
    data_path_name = task_name+"_"+head_camera_type+"_pkl"
    print(f'read data from path:{os.path.join("data/", data_path_name)}')

    # 设备配置
    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"Using device: {device}")

    # 图像预处理
    from torchvision import transforms
    preprocess = transforms.Compose([
        transforms.ToTensor(),
        transforms.Resize((224, 224)),
    ])

    # 模型加载
    if args.use_video_diffusion and VIDEO_DIFFUSION_AVAILABLE:
        if args.vae_path is None or args.transformer_path is None:
            raise ValueError("When using video diffusion, both --vae_path and --transformer_path must be specified")

        print("Loading video diffusion models...")
        # 确定text encoder路径
        text_encoder_path = getattr(args, 'text_encoder1', None) or getattr(args, 'text_encoder2', None)

        vae_model, guidance_model, text_encoder_1, text_encoder_2, tokenizer_1, tokenizer_2 = load_video_diffusion_models(
            args.vae_path, args.transformer_path, device, text_encoder_path
        )
        pre_latent_model = None
        print("Video diffusion models loaded successfully")
    else:
        print("warning: video diffusion models not available, falling back to dummy model")

    # 读取所有instructions（假设为统一json文件）
    # root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    root_path = "/media/jiayueru/Codes/RoboTwin"
    instruction_path = f"{root_path}/data/instructions/{task_name}.json"
    with open(instruction_path, 'r') as f_instr:
        instruction_dict = json.load(f_instr)
    instructions = instruction_dict['instructions']

    # save_path = f"./policy/RDT/processed_data/{task_name}_{head_camera_type}_{num}"
    save_path = f"{root_path}/policy/RDT/processed_data/{task_name}_{head_camera_type}_{num}"
    prelatent_path = os.path.join(save_path, "prelatent")
    os.makedirs(prelatent_path, exist_ok=True)

    for i in range(num):
        print(f"\n=== Processing episode {i} ===")
        subfolder_name = f"episode{i}"
        subfolder_path = os.path.join(f"{root_path}/data/", data_path_name, subfolder_name)
        if not os.path.isdir(subfolder_path):
            print(f"[Warning] {subfolder_path} does not exist, skipping.")
            continue
        # 取每个episode的instruction
        if i < len(instructions):
            instruction = instructions[i]
        else:
            instruction = instructions[-1]  # fallback

        # 根据模型类型选择不同的特征提取方法
        if args.use_video_diffusion and vae_model is not None and guidance_model is not None:
            cam_high_pre_features = extract_pre_latent_features_with_video_diffusion(
                subfolder_path, T, vae_model, guidance_model, preprocess, device, instruction,
                episode_idx=i, chunk_size=args.chunk_size, latent_slice_size=args.latent_slice_size,
                text_encoder_1=text_encoder_1, text_encoder_2=text_encoder_2,
                tokenizer_1=tokenizer_1, tokenizer_2=tokenizer_2
            )

            # 保存text embeddings (如果使用了text encoder)
            if text_encoder_1 is not None and text_encoder_2 is not None:
                save_text_embeddings_for_episode(
                    instruction, text_encoder_1, text_encoder_2, tokenizer_1, tokenizer_2,
                    device, prelatent_path, i
                )
        else:
            cam_high_pre_features = extract_pre_latent_features(
                subfolder_path, T, pre_latent_model, preprocess, device, instruction, episode_idx=i
            )

        hdf5path = os.path.join(prelatent_path, f'episode_{i}_prelatent.hdf5')
        with h5py.File(hdf5path, 'w') as f:
            f.create_dataset('cam_high_pre_features', data=cam_high_pre_features)
        print(f"[Episode {i}] Saved pre-latent features to {hdf5path}")

if __name__ == "__main__":
    main()
