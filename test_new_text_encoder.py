#!/usr/bin/env python3
"""
测试新的text encoder配置
"""

import os
import sys
import torch
from types import SimpleNamespace

def test_framepack_text_encoder():
    """测试framepack text encoder加载"""
    print("🔍 Testing framepack text encoder loading...")
    
    # 基础路径
    base_path = "/media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773"
    
    # 检查基础路径
    if not os.path.exists(base_path):
        print(f"❌ Base path does not exist: {base_path}")
        return False
    
    try:
        # 导入framepack utils
        from frame_pack.framepack_utils import load_text_encoder1, load_text_encoder2
        print("✅ Successfully imported framepack text encoder functions")
        
        # 创建args对象
        args = SimpleNamespace()
        args.text_encoder1 = base_path
        args.text_encoder2 = base_path
        
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"Using device: {device}")
        
        # 加载text encoder 1
        print("Loading text encoder 1...")
        tokenizer_1, text_encoder_1 = load_text_encoder1(args, fp8_llm=False, device=device)
        print(f"✅ Text encoder 1 loaded: {type(text_encoder_1)}")
        
        # 加载text encoder 2
        print("Loading text encoder 2...")
        tokenizer_2, text_encoder_2 = load_text_encoder2(args)
        text_encoder_2.to(device)
        print(f"✅ Text encoder 2 loaded: {type(text_encoder_2)}")
        
        # 测试编码
        print("Testing text encoding...")
        test_prompt = "Pick up the red block"
        
        # 测试hunyuan编码
        from hunyuan_model import hunyuan
        llama_vec, clip_l_pooler = hunyuan.encode_prompt_conds(
            test_prompt, text_encoder_1, text_encoder_2, tokenizer_1, tokenizer_2
        )
        
        print(f"✅ Encoding successful!")
        print(f"   LLaMA vector shape: {llama_vec.shape}")
        print(f"   CLIP pooler shape: {clip_l_pooler.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_preprocess_integration():
    """测试与preprocess_rdt.py的集成"""
    print("\n🔧 Testing preprocess_rdt.py integration...")
    
    try:
        from dataset.preprocess_rdt import load_video_diffusion_models
        
        base_path = "/media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773"
        vae_path = f"{base_path}/vae/diffusion_pytorch_model.safetensors"
        transformer_path = "/media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7"
        
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        print("Testing model loading function...")
        vae_model, guidance_model, text_encoder_1, text_encoder_2, tokenizer_1, tokenizer_2 = load_video_diffusion_models(
            vae_path, transformer_path, device, base_path
        )
        
        if text_encoder_1 is not None and text_encoder_2 is not None:
            print("✅ Text encoders loaded successfully in preprocess_rdt.py")
            print(f"   Text encoder 1: {type(text_encoder_1)}")
            print(f"   Text encoder 2: {type(text_encoder_2)}")
            print(f"   Tokenizer 1: {type(tokenizer_1)}")
            print(f"   Tokenizer 2: {type(tokenizer_2)}")
            return True
        else:
            print("❌ Text encoders not loaded")
            return False
            
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 New Text Encoder Configuration Test")
    print("=" * 50)
    
    # 测试framepack text encoder
    framepack_test = test_framepack_text_encoder()
    
    # 测试集成
    integration_test = test_preprocess_integration()
    
    print("\n" + "=" * 50)
    print("📊 Test Results")
    print("=" * 50)
    
    if framepack_test:
        print("✅ Framepack text encoder: PASS")
    else:
        print("❌ Framepack text encoder: FAIL")
    
    if integration_test:
        print("✅ Preprocess integration: PASS")
    else:
        print("❌ Preprocess integration: FAIL")
    
    if framepack_test and integration_test:
        print("\n🎉 All tests passed! Text encoder configuration is working correctly.")
        print("\n💡 You can now use the VSCode launch configuration:")
        print("   'Dataset Video Diffusion + Text Encoder'")
    else:
        print("\n⚠️  Some tests failed. Please check the issues above.")

if __name__ == "__main__":
    main()
