# Guidance实现：直接使用Transformer Forward

## 问题描述

原始的guidance实现存在以下问题：
1. 使用复杂的k_model包装器，增加了不必要的复杂性
2. 设备和数据类型不一致导致的错误
3. 缺少image_embeddings参数的正确处理

## 修复内容

### 1. 直接使用Transformer Forward（不使用k_model包装器）

**修改前：**
```python
# 使用复杂的k_model包装器
k_model = guidance_model.fm_wrapper(guidance_model.transformer)
extra_args = {...}  # 复杂的参数结构
_ = k_model(x, sigma, **extra_args)
```

**修改后：**
```python
# 直接使用transformer forward，简化调用
timestep = (sigma * 1000.0).to(guidance_model.dtype)
_ = guidance_model.transformer(
    hidden_states=x.to(guidance_model.dtype),
    timestep=timestep,
    encoder_hidden_states=encoder_hidden_states,
    encoder_attention_mask=attention_mask,
    pooled_projections=pooled_projections,
    guidance=distilled_guidance,
    image_embeddings=image_embeddings,  # 正确处理image_embeddings
    return_dict=False
)
```

### 2. 正确处理image_embeddings参数

**添加的image_embeddings处理：**
```python
# 创建虚拟的image_embeddings，与framepack保持一致
# framepack使用image_embeddings参数，形状为 (B, 576, 1152) 模仿CLIP vision的输出
image_embeddings = torch.randn(B, 576, 1152, device=device, dtype=guidance_model.dtype)
```

**在transformer调用中使用：**
```python
_ = guidance_model.transformer(
    # ... 其他参数
    image_embeddings=image_embeddings,  # 添加image_embeddings处理
    return_dict=False
)
```

### 3. 设备和数据类型一致性

**确保所有tensor在正确设备上：**
```python
# 确保guidance_model的transformer在正确的设备上
guidance_model.transformer.to(device)

# 确保所有text embeddings在正确的设备和数据类型上
encoder_hidden_states = encoder_hidden_states.to(device=device, dtype=guidance_model.dtype)
attention_mask = attention_mask.to(device=device)
pooled_projections = pooled_projections.to(device=device, dtype=guidance_model.dtype)

# 创建distilled guidance，确保在正确设备上
distilled_guidance = torch.tensor([6000.0] * B, device=device, dtype=guidance_model.dtype)

# 创建image_embeddings，确保在正确设备上
image_embeddings = torch.randn(B, 576, 1152, device=device, dtype=guidance_model.dtype)
```

### 4. 简化的timestep处理

**timestep转换：**
```python
# 创建虚拟的timestep，转换为transformer期望的格式
timestep = (sigma * 1000.0).to(guidance_model.dtype)  # 转换为guidance scale
```

## 关键改进

### 1. 简化的调用模式
- 移除了复杂的k_model包装器
- 直接调用transformer.forward()
- 减少了中间层的复杂性

### 2. 正确的参数处理
- 添加了image_embeddings参数
- 确保所有参数的设备和数据类型一致
- 简化的timestep处理

### 3. 更好的设备管理
- 确保所有tensor在正确的设备上
- 适当的内存管理和清理
- 避免设备不匹配错误

### 4. 与FramePack的兼容性
- 使用相同的参数名称（image_embeddings）
- 保持数据格式的一致性
- 遵循framepack的调用约定

## 完整的实现

```python
def process_latent_through_transformer(latent_slice, guidance_model, instruction, device, ...):
    B, C, T, H, W = latent_slice.shape
    
    # 设备管理
    guidance_model.transformer.to(device)
    
    # 准备输入数据
    x = latent_slice.to(device=device, dtype=torch.float32)
    sigma = torch.tensor([0.0] * B, device=device, dtype=torch.float32)
    
    # 准备text embeddings
    encoder_hidden_states = encoder_hidden_states.to(device=device, dtype=guidance_model.dtype)
    attention_mask = attention_mask.to(device=device)
    pooled_projections = pooled_projections.to(device=device, dtype=guidance_model.dtype)
    
    # 创建guidance和image_embeddings
    distilled_guidance = torch.tensor([6000.0] * B, device=device, dtype=guidance_model.dtype)
    image_embeddings = torch.randn(B, 576, 1152, device=device, dtype=guidance_model.dtype)
    
    # 创建timestep
    timestep = (sigma * 1000.0).to(guidance_model.dtype)
    
    # 直接调用transformer
    with torch.no_grad():
        _ = guidance_model.transformer(
            hidden_states=x.to(guidance_model.dtype),
            timestep=timestep,
            encoder_hidden_states=encoder_hidden_states,
            encoder_attention_mask=attention_mask,
            pooled_projections=pooled_projections,
            guidance=distilled_guidance,
            image_embeddings=image_embeddings,
            return_dict=False
        )
    
    # 提取特征
    motion_features, motion_channels = guidance_model.load_features(moft=True)
    
    # 内存清理
    guidance_model.transformer.to("cpu")
    if device.type == "cuda":
        torch.cuda.empty_cache()
    
    return processed_features
```

## 验证

创建了测试文件`test_direct_transformer_call.py`来验证：
1. 直接transformer调用的正确性
2. image_embeddings参数的处理
3. 设备和数据类型一致性
4. 简化的调用流程

## 结果

修复后的实现：
- ✅ 简化了调用流程（移除k_model包装器）
- ✅ 正确处理image_embeddings参数
- ✅ 解决了设备不匹配的错误
- ✅ 保持与framepack的兼容性
- ✅ 适当的内存管理
- ✅ 更清晰和直接的代码结构

这确保了guidance实现既简单又正确，避免了不必要的复杂性和设备相关的错误。
