# Launch Configuration Guide

这个文档说明了如何使用配置好的 VSCode launch.json 来运行不同模式的数据预处理。

## 🚀 可用的配置

### 1. Dataset Main (Dummy Model)
**用途**: 使用原始的dummy模型进行基本的特征提取
**特点**: 
- 快速运行，不需要大型模型
- 适合测试和调试
- 处理50个episodes

**使用场景**: 
- 初次测试代码
- 验证数据加载流程
- 快速原型开发

### 2. Dataset Video Diffusion (Basic)
**用途**: 使用video diffusion model但不使用text encoder
**特点**:
- 使用HunyuanVideo VAE进行视频编码
- 使用FramePack transformer进行特征提取
- 处理10个episodes（较少数量用于测试）
- 使用虚拟text embeddings

**模型路径**:
- VAE: HunyuanVideo VAE模型
- Transformer: FramePack F1 I2V模型

### 3. Dataset Video Diffusion + Text Encoder ⭐
**用途**: 完整的video diffusion + text encoder功能
**特点**:
- 使用HunyuanVideo VAE进行视频编码
- 使用FramePack transformer进行特征提取
- 使用HunyuanVideo text encoders处理指令
- 自动保存text embeddings
- 处理5个episodes（完整功能测试）

**模型路径**:
- VAE: HunyuanVideo VAE模型
- Transformer: FramePack F1 I2V模型
- Text Encoder: HunyuanVideo text encoders

## 📁 模型路径说明

### VAE模型
```
/media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/vae/diffusion_pytorch_model.safetensors
```

### Transformer模型
```
/media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7
```

### Text Encoder模型
```
/media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773
```

## 🎯 推荐使用流程

### 第一步: 测试基本功能
使用 **"Dataset Main (Dummy Model)"** 配置:
- 验证数据加载正常
- 确认指令JSON文件存在
- 检查输出路径设置

### 第二步: 测试Video Diffusion
使用 **"Dataset Video Diffusion (Basic)"** 配置:
- 验证VAE和transformer模型加载
- 测试video diffusion处理流程
- 检查latent特征提取

### 第三步: 完整功能测试
使用 **"Dataset Video Diffusion + Text Encoder"** 配置:
- 测试完整的text encoder功能
- 验证text embeddings保存
- 确认所有功能正常工作

## 📊 参数说明

### 通用参数
- `block_handover`: 任务名称
- `D435`: 相机类型
- `--T 9`: 时间窗口大小

### Video Diffusion参数
- `--use_video_diffusion`: 启用video diffusion模式
- `--chunk_size`: VAE处理的chunk大小
- `--latent_slice_size`: Latent切片大小

### 模型路径参数
- `--vae_path`: VAE模型路径
- `--transformer_path`: Transformer模型路径
- `--text_encoder_path`: Text encoder模型路径（可选）

## 🔧 自定义配置

如果需要修改配置，可以调整以下参数：

### 修改处理的episodes数量
```json
"args": [
    "block_handover",
    "D435",
    "20",  // 修改这个数字
    "--T",
    "9"
]
```

### 修改任务名称
```json
"args": [
    "your_task_name",  // 修改任务名称
    "D435",
    "10",
    "--T",
    "9"
]
```

### 调整性能参数
```json
"--chunk_size",
"4",  // 减少以节省内存
"--latent_slice_size",
"8"   // 减少以节省内存
```

## 📝 指令文件

确保在 `data/instructions/` 目录下有对应的JSON文件：
- `block_handover.json`: 包含任务指令列表
- 格式参考已创建的示例文件

## 🚨 注意事项

1. **内存需求**: Video diffusion模式需要大量GPU内存
2. **模型路径**: 确保所有模型路径存在且可访问
3. **数据路径**: 确认episode数据存在于正确路径
4. **Python环境**: 使用正确的conda环境 (`musubi`)

## 🎉 开始使用

1. 在VSCode中打开调试面板 (Ctrl+Shift+D)
2. 选择想要的配置
3. 点击绿色播放按钮开始运行
4. 在集成终端中查看输出和进度

祝你使用愉快！🚀
