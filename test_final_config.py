#!/usr/bin/env python3
"""
最终配置测试脚本 - 验证所有修改是否正确
"""

import os
import sys
import torch
from types import SimpleNamespace

def test_hunyuan_vae_encode():
    """测试hunyuan.vae_encode函数"""
    print("🔍 Testing hunyuan.vae_encode...")
    
    try:
        from frame_pack import hunyuan
        from frame_pack.framepack_utils import load_vae as load_framepack_vae
        
        # 基础路径
        base_path = "/media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773"
        vae_path = f"{base_path}/vae/diffusion_pytorch_model.safetensors"
        
        if not os.path.exists(vae_path):
            print(f"❌ VAE path does not exist: {vae_path}")
            return False
        
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"Using device: {device}")
        
        # 加载VAE
        print("Loading VAE...")
        vae_model = load_framepack_vae(vae_path, None, None, device)
        vae_model.eval()
        print(f"✅ VAE loaded: {type(vae_model)}")
        print(f"   Device: {vae_model.device}")
        print(f"   Dtype: {vae_model.dtype}")
        
        # 创建测试视频tensor
        print("Creating test video tensor...")
        # 模拟视频数据: (1, C, T, H, W)
        video_tensor = torch.randn(1, 3, 4, 224, 224, device=device, dtype=vae_model.dtype)
        video_tensor = video_tensor * 2.0 - 1.0  # 归一化到[-1, 1]
        
        # 测试VAE编码
        print("Testing VAE encoding...")
        with torch.no_grad():
            latents = hunyuan.vae_encode(video_tensor, vae_model)
        
        print(f"✅ VAE encoding successful!")
        print(f"   Input shape: {video_tensor.shape}")
        print(f"   Output shape: {latents.shape}")
        print(f"   Output dtype: {latents.dtype}")
        print(f"   Output device: {latents.device}")
        
        # 移到CPU
        latents_cpu = latents.cpu()
        print(f"   CPU shape: {latents_cpu.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_text_encoder_integration():
    """测试text encoder集成"""
    print("\n🔤 Testing text encoder integration...")
    
    try:
        from frame_pack.framepack_utils import load_text_encoder1, load_text_encoder2
        from frame_pack import hunyuan
        
        base_path = "/media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773"
        
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 创建args对象
        args = SimpleNamespace()
        args.text_encoder1 = base_path
        args.text_encoder2 = base_path
        
        # 加载text encoders
        print("Loading text encoders...")
        tokenizer_1, text_encoder_1 = load_text_encoder1(args, fp8_llm=False, device=device)
        tokenizer_2, text_encoder_2 = load_text_encoder2(args)
        text_encoder_2.to(device)
        
        print(f"✅ Text encoders loaded")
        print(f"   Text encoder 1: {type(text_encoder_1)}")
        print(f"   Text encoder 2: {type(text_encoder_2)}")
        
        # 测试编码
        test_prompt = "Pick up the red block and hand it over to the human"
        print(f"Testing encoding with prompt: '{test_prompt}'")
        
        llama_vec, clip_l_pooler = hunyuan.encode_prompt_conds(
            test_prompt, text_encoder_1, text_encoder_2, tokenizer_1, tokenizer_2
        )
        
        print(f"✅ Text encoding successful!")
        print(f"   LLaMA vector shape: {llama_vec.shape}")
        print(f"   CLIP pooler shape: {clip_l_pooler.shape}")
        
        # 测试crop_or_pad_yield_mask
        from hunyuan_model.text_encoder import crop_or_pad_yield_mask
        llama_vec_cropped, attention_mask = crop_or_pad_yield_mask(llama_vec, length=512)
        print(f"✅ Attention mask processing successful!")
        print(f"   Cropped LLaMA vector shape: {llama_vec_cropped.shape}")
        print(f"   Attention mask shape: {attention_mask.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_preprocess_function():
    """测试preprocess_rdt.py中的函数"""
    print("\n🔧 Testing preprocess_rdt.py functions...")
    
    try:
        from dataset.preprocess_rdt import load_video_diffusion_models, encode_instruction_with_hunyuan_text_encoder
        
        base_path = "/media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773"
        vae_path = f"{base_path}/vae/diffusion_pytorch_model.safetensors"
        transformer_path = "/media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7"
        
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        print("Testing load_video_diffusion_models...")
        vae_model, guidance_model, text_encoder_1, text_encoder_2, tokenizer_1, tokenizer_2 = load_video_diffusion_models(
            vae_path, transformer_path, device, base_path
        )
        
        print(f"✅ Models loaded successfully!")
        print(f"   VAE: {type(vae_model)}")
        print(f"   Guidance: {type(guidance_model)}")
        print(f"   Text encoder 1: {type(text_encoder_1)}")
        print(f"   Text encoder 2: {type(text_encoder_2)}")
        
        if text_encoder_1 is not None and text_encoder_2 is not None:
            print("Testing encode_instruction_with_hunyuan_text_encoder...")
            test_instruction = "Pick up the red block and hand it over to the human"
            
            llama_vec, llama_attention_mask, clip_l_pooler = encode_instruction_with_hunyuan_text_encoder(
                test_instruction, text_encoder_1, text_encoder_2, tokenizer_1, tokenizer_2, device
            )
            
            if llama_vec is not None:
                print(f"✅ Instruction encoding successful!")
                print(f"   LLaMA vector shape: {llama_vec.shape}")
                print(f"   Attention mask shape: {llama_attention_mask.shape}")
                print(f"   CLIP pooler shape: {clip_l_pooler.shape}")
            else:
                print("❌ Instruction encoding failed")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 Final Configuration Test Suite")
    print("=" * 60)
    
    # 测试VAE编码
    vae_test = test_hunyuan_vae_encode()
    
    # 测试text encoder
    text_test = test_text_encoder_integration()
    
    # 测试preprocess函数
    preprocess_test = test_preprocess_function()
    
    print("\n" + "=" * 60)
    print("📊 Final Test Results")
    print("=" * 60)
    
    if vae_test:
        print("✅ VAE encoding (hunyuan.vae_encode): PASS")
    else:
        print("❌ VAE encoding (hunyuan.vae_encode): FAIL")
    
    if text_test:
        print("✅ Text encoder integration: PASS")
    else:
        print("❌ Text encoder integration: FAIL")
    
    if preprocess_test:
        print("✅ Preprocess functions: PASS")
    else:
        print("❌ Preprocess functions: FAIL")
    
    if vae_test and text_test and preprocess_test:
        print("\n🎉 All tests passed! Configuration is ready for use.")
        print("\n💡 You can now run the VSCode configuration:")
        print("   'Dataset Video Diffusion + Text Encoder'")
        print("\n🚀 Or run directly:")
        print("   python dataset/preprocess_rdt.py block_handover D435 2 --T 9 --use_video_diffusion \\")
        print("     --vae_path /path/to/vae/model.safetensors \\")
        print("     --transformer_path /path/to/transformer/model \\")
        print("     --text_encoder1 /path/to/text/encoder/base")
    else:
        print("\n⚠️  Some tests failed. Please check the issues above.")
        print("\n🔧 Common fixes:")
        print("- Ensure all model files are downloaded and accessible")
        print("- Check CUDA availability and memory")
        print("- Verify Python environment has all required packages")

if __name__ == "__main__":
    main()
