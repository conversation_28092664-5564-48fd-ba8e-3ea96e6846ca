#!/usr/bin/env python3
"""
测试直接调用transformer的guidance实现
"""

import sys
sys.path.append('/media/jiayueru/Codes/RoboTwin/policy/RDT')

import torch
import torch.nn as nn

def test_direct_transformer_call():
    """测试直接调用transformer而不使用k_model包装器"""
    
    print("Testing direct transformer call with image_embeddings...")
    
    # 创建模拟的transformer
    class MockTransformer(nn.Module):
        def __init__(self):
            super().__init__()
            self.conv = nn.Conv3d(16, 16, kernel_size=3, padding=1)
            self.dtype = torch.bfloat16
            
        def forward(self, hidden_states, timestep, encoder_hidden_states, 
                   encoder_attention_mask, pooled_projections, guidance, 
                   image_embeddings=None, return_dict=False):
            # 验证所有参数
            print(f"  ✓ hidden_states: {hidden_states.shape}, device: {hidden_states.device}, dtype: {hidden_states.dtype}")
            print(f"  ✓ timestep: {timestep.shape}, device: {timestep.device}, dtype: {timestep.dtype}")
            print(f"  ✓ encoder_hidden_states: {encoder_hidden_states.shape}, device: {encoder_hidden_states.device}")
            print(f"  ✓ pooled_projections: {pooled_projections.shape}, device: {pooled_projections.device}")
            print(f"  ✓ guidance: {guidance.shape}, device: {guidance.device}")
            
            if image_embeddings is not None:
                print(f"  ✓ image_embeddings: {image_embeddings.shape}, device: {image_embeddings.device}, dtype: {image_embeddings.dtype}")
            else:
                print("  ⚠ image_embeddings is None")
            
            # 简单的输出
            output = self.conv(hidden_states)
            return (output,) if not return_dict else {"x": output}
    
    class MockGuidanceModel:
        def __init__(self, device):
            self.transformer = MockTransformer()
            self.dtype = torch.bfloat16
            self.device = device
            
        def load_features(self, moft=False):
            # 返回模拟特征
            motion_features = {"block_19": torch.randn(3072, 4, 32, 48)}
            motion_channels = {"block_19": list(range(100))}
            if moft:
                return motion_features, motion_channels
            return motion_features
    
    # 测试设备一致性
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Testing on device: {device}")
    
    # 创建模拟数据
    latent_slice = torch.randn(1, 16, 4, 32, 48, dtype=torch.float32)
    mock_guidance = MockGuidanceModel(device)
    
    # 确保模型在正确设备上
    mock_guidance.transformer.to(device)
    
    print(f"Transformer device: {next(mock_guidance.transformer.parameters()).device}")
    print(f"Transformer dtype: {next(mock_guidance.transformer.parameters()).dtype}")
    
    # 模拟process_latent_through_transformer的关键步骤
    try:
        # 将输入移到正确设备
        x = latent_slice.to(device=device, dtype=torch.float32)
        sigma = torch.tensor([0.0], device=device, dtype=torch.float32)
        
        # 创建模拟text embeddings
        B = x.shape[0]
        encoder_hidden_states = torch.randn(B, 256, 4096, device=device, dtype=mock_guidance.dtype)
        attention_mask = torch.ones(B, 256, device=device, dtype=torch.bool)
        pooled_projections = torch.randn(B, 768, device=device, dtype=mock_guidance.dtype)
        distilled_guidance = torch.tensor([6000.0] * B, device=device, dtype=mock_guidance.dtype)
        
        # 创建image_embeddings，与framepack保持一致
        image_embeddings = torch.randn(B, 576, 1152, device=device, dtype=mock_guidance.dtype)
        
        # 创建timestep
        timestep = (sigma * 1000.0).to(mock_guidance.dtype)
        
        print("\nCalling transformer directly...")
        with torch.no_grad():
            result = mock_guidance.transformer(
                hidden_states=x.to(mock_guidance.dtype),
                timestep=timestep,
                encoder_hidden_states=encoder_hidden_states,
                encoder_attention_mask=attention_mask,
                pooled_projections=pooled_projections,
                guidance=distilled_guidance,
                image_embeddings=image_embeddings,
                return_dict=False
            )
            
        print(f"✓ Transformer call successful! Result shape: {result[0].shape}")
        print(f"✓ Result device: {result[0].device}, dtype: {result[0].dtype}")
        
        # 测试load_features
        motion_features, motion_channels = mock_guidance.load_features(moft=True)
        print(f"✓ load_features successful! Features: {list(motion_features.keys())}")
        
        print("\n🎉 All tests passed! Direct transformer call with image_embeddings works!")
        print("✓ Device consistency maintained")
        print("✓ image_embeddings parameter correctly passed")
        print("✓ Data types are consistent")
        print("✓ Direct transformer call (no k_model wrapper)")
        
        return True
        
    except Exception as e:
        print(f"✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_direct_transformer_call()
    if success:
        print("\n✅ Direct transformer call implementation is working correctly!")
    else:
        print("\n❌ There are still issues to fix.")
