#!/usr/bin/env python3
"""
快速测试脚本 - 直接运行video diffusion预处理功能
"""

import os
import sys
import subprocess

def run_dummy_test():
    """运行dummy模型测试"""
    print("🚀 Running dummy model test...")
    
    cmd = [
        sys.executable,
        "dataset/preprocess_rdt.py",
        "block_handover",
        "D435", 
        "2",  # 只处理2个episodes
        "--T", "9"
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        if result.returncode == 0:
            print("✅ Dummy model test passed!")
            return True
        else:
            print(f"❌ Dummy model test failed:")
            print(f"STDOUT: {result.stdout}")
            print(f"STDERR: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("⏰ Dummy model test timed out")
        return False
    except Exception as e:
        print(f"❌ Error running dummy model test: {e}")
        return False

def run_video_diffusion_test():
    """运行video diffusion测试"""
    print("🎬 Running video diffusion test...")
    
    # 检查模型路径
    vae_path = "/media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/vae/diffusion_pytorch_model.safetensors"
    transformer_path = "/media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7"
    text_encoder_path = "/media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773"
    
    # 检查路径是否存在
    if not os.path.exists(vae_path):
        print(f"❌ VAE model not found: {vae_path}")
        return False
    
    if not os.path.exists(transformer_path):
        print(f"❌ Transformer model not found: {transformer_path}")
        return False
    
    if not os.path.exists(text_encoder_path):
        print(f"❌ Text encoder not found: {text_encoder_path}")
        return False
    
    cmd = [
        sys.executable,
        "dataset/preprocess_rdt.py",
        "block_handover",
        "D435",
        "1",  # 只处理1个episode
        "--T", "9",
        "--use_video_diffusion",
        "--vae_path", vae_path,
        "--transformer_path", transformer_path,
        "--text_encoder_path", text_encoder_path,
        "--chunk_size", "2",
        "--latent_slice_size", "4"
    ]
    
    try:
        print("⏳ This may take a while (loading large models)...")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)  # 30分钟超时
        if result.returncode == 0:
            print("✅ Video diffusion test passed!")
            return True
        else:
            print(f"❌ Video diffusion test failed:")
            print(f"STDOUT: {result.stdout}")
            print(f"STDERR: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("⏰ Video diffusion test timed out (30 minutes)")
        return False
    except Exception as e:
        print(f"❌ Error running video diffusion test: {e}")
        return False

def check_prerequisites():
    """检查先决条件"""
    print("🔍 Checking prerequisites...")
    
    # 检查Python环境
    print(f"Python: {sys.executable}")
    
    # 检查关键文件
    files_to_check = [
        "dataset/preprocess_rdt.py",
        "data/instructions/block_handover.json"
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            return False
    
    # 检查数据目录
    data_dir = "/media/jiayueru/Codes/RoboTwin/data/block_handover_D435_pkl"
    if os.path.exists(data_dir):
        episodes = [d for d in os.listdir(data_dir) if d.startswith('episode')]
        print(f"✅ Data directory: {data_dir} ({len(episodes)} episodes)")
        if len(episodes) == 0:
            print("⚠️  No episodes found in data directory")
            return False
    else:
        print(f"❌ Data directory: {data_dir}")
        return False
    
    return True

def main():
    """主函数"""
    print("🧪 Quick Test Suite for Video Diffusion Preprocessing")
    print("=" * 60)
    
    # 检查先决条件
    if not check_prerequisites():
        print("\n❌ Prerequisites check failed. Please fix the issues above.")
        return
    
    print("\n✅ Prerequisites check passed!")
    
    # 询问用户想要运行哪个测试
    print("\n🎯 Which test would you like to run?")
    print("1. Dummy model test (fast, basic functionality)")
    print("2. Video diffusion test (slow, full functionality)")
    print("3. Both tests")
    
    choice = input("\nEnter your choice (1/2/3): ").strip()
    
    if choice == "1":
        run_dummy_test()
    elif choice == "2":
        run_video_diffusion_test()
    elif choice == "3":
        print("\n" + "="*30)
        print("Running dummy model test first...")
        if run_dummy_test():
            print("\n" + "="*30)
            print("Running video diffusion test...")
            run_video_diffusion_test()
        else:
            print("❌ Dummy test failed, skipping video diffusion test")
    else:
        print("❌ Invalid choice. Please run the script again.")

if __name__ == "__main__":
    main()
