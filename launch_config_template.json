{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Dataset Video Diffusion + Text Encoder (Template)",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/dataset/preprocess_rdt.py",
            "console": "integratedTerminal",
            "justMyCode": false,
            "env": {
                "PYTHONPATH": "${workspaceFolder}",
                "SHELL": "/usr/bin/zsh"
            },
            "args": [
                "block_handover",
                "D435",
                "3",
                "--T",
                "9",
                "--use_video_diffusion",
                "--vae_path",
                "YOUR_VAE_MODEL_PATH_HERE",
                "--transformer_path",
                "YOUR_TRANSFORMER_MODEL_PATH_HERE",
                "--text_encoder_path",
                "YOUR_TEXT_ENCODER_PATH_HERE",
                "--chunk_size",
                "2",
                "--latent_slice_size",
                "4"
            ],
            "python": "/media/miniconda3/envs/musubi/bin/python"
        }
    ]
}

// 使用说明:
// 1. 将 YOUR_VAE_MODEL_PATH_HERE 替换为你的VAE模型路径
// 2. 将 YOUR_TRANSFORMER_MODEL_PATH_HERE 替换为你的transformer模型路径  
// 3. 将 YOUR_TEXT_ENCODER_PATH_HERE 替换为你的text encoder路径
// 4. 调整 chunk_size 和 latent_slice_size 以适应你的GPU内存
// 5. 调整处理的episodes数量 (当前设置为3个用于快速测试)

// 常见模型路径示例:
// VAE: "path/to/hunyuan/vae/diffusion_pytorch_model.safetensors"
// Transformer: "path/to/framepack/model/directory"
// Text Encoder: "path/to/hunyuan/text_encoder/directory"
