#!/usr/bin/env python3
"""
示例脚本：如何加载和使用保存的text embeddings

这个脚本展示了如何读取由preprocess_rdt.py生成的text embeddings文件
"""

import os
import h5py
import numpy as np
import torch

def load_text_embeddings(embeddings_path):
    """
    加载text embeddings文件
    
    Args:
        embeddings_path: text embeddings文件路径
    
    Returns:
        dict: 包含所有embeddings的字典
    """
    if not os.path.exists(embeddings_path):
        print(f"Error: File {embeddings_path} does not exist")
        return None
    
    embeddings = {}
    
    try:
        with h5py.File(embeddings_path, 'r') as f:
            # 加载LLM embeddings
            if 'llama_vec' in f:
                embeddings['llama_vec'] = torch.from_numpy(f['llama_vec'][:])
                print(f"Loaded llama_vec: {embeddings['llama_vec'].shape}")
            
            # 加载attention mask
            if 'llama_attention_mask' in f:
                embeddings['llama_attention_mask'] = torch.from_numpy(f['llama_attention_mask'][:])
                print(f"Loaded llama_attention_mask: {embeddings['llama_attention_mask'].shape}")
            
            # 加载CLIP embeddings
            if 'clip_l_pooler' in f:
                embeddings['clip_l_pooler'] = torch.from_numpy(f['clip_l_pooler'][:])
                print(f"Loaded clip_l_pooler: {embeddings['clip_l_pooler'].shape}")
            
            # 加载原始指令
            if 'instruction' in f:
                instruction_bytes = f['instruction'][:]
                embeddings['instruction'] = instruction_bytes.decode('utf-8')
                print(f"Loaded instruction: '{embeddings['instruction']}'")
        
        return embeddings
        
    except Exception as e:
        print(f"Error loading embeddings: {e}")
        return None

def load_episode_data(data_dir, episode_idx):
    """
    加载指定episode的所有数据
    
    Args:
        data_dir: 数据目录路径
        episode_idx: episode索引
    
    Returns:
        tuple: (pre_latent_features, text_embeddings)
    """
    # 加载pre-latent features
    prelatent_path = os.path.join(data_dir, f'episode_{episode_idx}_prelatent.hdf5')
    pre_latent_features = None
    
    if os.path.exists(prelatent_path):
        try:
            with h5py.File(prelatent_path, 'r') as f:
                pre_latent_features = torch.from_numpy(f['cam_high_pre_features'][:])
                print(f"Loaded pre-latent features: {pre_latent_features.shape}")
        except Exception as e:
            print(f"Error loading pre-latent features: {e}")
    else:
        print(f"Pre-latent file not found: {prelatent_path}")
    
    # 加载text embeddings
    text_embeddings_path = os.path.join(data_dir, f'episode_{episode_idx}_text_embeddings.hdf5')
    text_embeddings = load_text_embeddings(text_embeddings_path)
    
    return pre_latent_features, text_embeddings

def demonstrate_usage():
    """
    演示如何使用加载的embeddings
    """
    # 示例数据目录
    data_dir = "/path/to/processed_data/task_name_camera_type_num/prelatent"
    episode_idx = 0
    
    print(f"Loading data for episode {episode_idx}...")
    pre_latent_features, text_embeddings = load_episode_data(data_dir, episode_idx)
    
    if text_embeddings is not None:
        print("\n=== Text Embeddings Analysis ===")
        
        # 分析LLM embeddings
        if 'llama_vec' in text_embeddings:
            llama_vec = text_embeddings['llama_vec']
            print(f"LLM embedding shape: {llama_vec.shape}")
            print(f"LLM embedding mean: {llama_vec.mean():.4f}")
            print(f"LLM embedding std: {llama_vec.std():.4f}")
        
        # 分析CLIP embeddings
        if 'clip_l_pooler' in text_embeddings:
            clip_pooler = text_embeddings['clip_l_pooler']
            print(f"CLIP embedding shape: {clip_pooler.shape}")
            print(f"CLIP embedding mean: {clip_pooler.mean():.4f}")
            print(f"CLIP embedding std: {clip_pooler.std():.4f}")
        
        # 显示指令
        if 'instruction' in text_embeddings:
            print(f"Instruction: '{text_embeddings['instruction']}'")
    
    if pre_latent_features is not None:
        print(f"\n=== Pre-latent Features Analysis ===")
        print(f"Pre-latent features shape: {pre_latent_features.shape}")
        print(f"Pre-latent features mean: {pre_latent_features.mean():.4f}")
        print(f"Pre-latent features std: {pre_latent_features.std():.4f}")

def batch_load_embeddings(data_dir, num_episodes):
    """
    批量加载多个episode的embeddings
    
    Args:
        data_dir: 数据目录路径
        num_episodes: episode数量
    
    Returns:
        list: 所有episode的embeddings列表
    """
    all_embeddings = []
    
    for i in range(num_episodes):
        print(f"\nLoading episode {i}...")
        _, text_embeddings = load_episode_data(data_dir, i)
        
        if text_embeddings is not None:
            all_embeddings.append(text_embeddings)
        else:
            print(f"Warning: No text embeddings found for episode {i}")
    
    print(f"\nLoaded embeddings for {len(all_embeddings)} episodes")
    return all_embeddings

def compare_instructions(embeddings_list):
    """
    比较不同episode的指令
    
    Args:
        embeddings_list: embeddings列表
    """
    print("\n=== Instruction Comparison ===")
    
    for i, embeddings in enumerate(embeddings_list):
        if 'instruction' in embeddings:
            print(f"Episode {i}: '{embeddings['instruction']}'")

if __name__ == "__main__":
    print("Text Embeddings Loading Example")
    print("=" * 40)
    
    # 注意：请修改这个路径为你的实际数据路径
    data_dir = "/path/to/your/processed_data/task_name_camera_type_num/prelatent"
    
    if not os.path.exists(data_dir):
        print(f"Warning: Data directory {data_dir} does not exist")
        print("Please modify the data_dir variable to point to your actual data directory")
    else:
        # 演示单个episode加载
        demonstrate_usage()
        
        # 演示批量加载
        # all_embeddings = batch_load_embeddings(data_dir, 5)
        # compare_instructions(all_embeddings)
