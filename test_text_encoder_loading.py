#!/usr/bin/env python3
"""
测试text encoder加载的专用脚本
"""

import os
import sys
import torch

def test_text_encoder_loading():
    """测试text encoder加载"""
    print("🔍 Testing text encoder loading...")

    # 基础路径
    base_path = "/media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773"

    # 检查基础路径
    if not os.path.exists(base_path):
        print(f"❌ Base path does not exist: {base_path}")
        return False

    print(f"✅ Base path exists: {base_path}")

    # 检查子目录
    subdirs = ["text_encoder", "text_encoder_2", "tokenizer", "tokenizer_2"]
    for subdir in subdirs:
        path = os.path.join(base_path, subdir)
        if os.path.exists(path):
            print(f"✅ {subdir}: {path}")
        else:
            print(f"❌ {subdir}: {path}")
            return False

    # 尝试导入必要的模块
    try:
        print("\n📦 Testing imports...")
        from frame_pack.framepack_utils import load_text_encoder1, load_text_encoder2
        from types import SimpleNamespace
        print("✅ Successfully imported load_text_encoder1 and load_text_encoder2 from framepack_utils")
    except ImportError as e:
        print(f"❌ Failed to import text encoder functions: {e}")
        return False

    # 尝试加载text encoders
    try:
        print("\n🚀 Testing text encoder loading...")
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"Using device: {device}")

        # 创建args对象，模仿选中代码的方式
        args = SimpleNamespace()
        args.text_encoder1 = base_path  # 基础路径，函数内部会处理子目录
        args.text_encoder2 = base_path  # 基础路径，函数内部会处理子目录

        print(f"Loading text_encoder1 from: {args.text_encoder1}")
        tokenizer_1, text_encoder_1 = load_text_encoder1(args, fp8_llm=False, device=device)
        print(f"✅ text_encoder_1 loaded successfully")
        print(f"   Text encoder type: {type(text_encoder_1)}")
        print(f"   Tokenizer type: {type(tokenizer_1)}")
        print(f"   Device: {text_encoder_1.device}")
        print(f"   Dtype: {text_encoder_1.dtype}")

        print(f"Loading text_encoder2 from: {args.text_encoder2}")
        tokenizer_2, text_encoder_2 = load_text_encoder2(args)
        text_encoder_2.to(device)
        print(f"✅ text_encoder_2 loaded successfully")
        print(f"   Text encoder type: {type(text_encoder_2)}")
        print(f"   Tokenizer type: {type(tokenizer_2)}")
        print(f"   Device: {text_encoder_2.device}")
        print(f"   Dtype: {text_encoder_2.dtype}")

        # 测试tokenizer访问
        print(f"\n🔤 Testing tokenizers...")
        print(f"✅ tokenizer_1: {type(tokenizer_1)}")
        print(f"✅ tokenizer_2: {type(tokenizer_2)}")

        # 测试简单的文本编码
        print(f"\n📝 Testing text encoding...")
        test_prompt = "Pick up the red block"

        try:
            # 测试hunyuan.encode_prompt_conds函数
            from hunyuan_model import hunyuan

            llama_vec, clip_l_pooler = hunyuan.encode_prompt_conds(
                test_prompt, text_encoder_1, text_encoder_2, tokenizer_1, tokenizer_2
            )

            print(f"✅ hunyuan.encode_prompt_conds successful")
            print(f"   LLaMA vector shape: {llama_vec.shape}")
            print(f"   CLIP pooler shape: {clip_l_pooler.shape}")

            # 测试crop_or_pad_yield_mask
            from hunyuan_model.text_encoder import crop_or_pad_yield_mask
            llama_vec_cropped, attention_mask = crop_or_pad_yield_mask(llama_vec, length=512)
            print(f"✅ crop_or_pad_yield_mask successful")
            print(f"   Cropped LLaMA vector shape: {llama_vec_cropped.shape}")
            print(f"   Attention mask shape: {attention_mask.shape}")

        except Exception as e:
            print(f"❌ Text encoding failed: {e}")
            import traceback
            traceback.print_exc()
            return False

        print(f"\n🎉 All text encoder tests passed!")
        return True

    except Exception as e:
        print(f"❌ Text encoder loading failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_hunyuan_encode_prompt_conds():
    """测试hunyuan.encode_prompt_conds函数"""
    print("\n🔧 Testing hunyuan.encode_prompt_conds...")

    try:
        from hunyuan_model import hunyuan
        print("✅ Successfully imported hunyuan module")

        # 检查是否有encode_prompt_conds函数
        if hasattr(hunyuan, 'encode_prompt_conds'):
            print("✅ encode_prompt_conds function found")
        else:
            print("❌ encode_prompt_conds function not found")
            return False

        return True

    except ImportError as e:
        print(f"❌ Failed to import hunyuan module: {e}")
        return False

def main():
    """主函数"""
    print("🧪 Text Encoder Loading Test")
    print("=" * 50)

    # 测试text encoder加载
    encoder_test = test_text_encoder_loading()

    # 测试hunyuan函数
    hunyuan_test = test_hunyuan_encode_prompt_conds()

    print("\n" + "=" * 50)
    print("📊 Test Results")
    print("=" * 50)

    if encoder_test:
        print("✅ Text encoder loading: PASS")
    else:
        print("❌ Text encoder loading: FAIL")

    if hunyuan_test:
        print("✅ Hunyuan functions: PASS")
    else:
        print("❌ Hunyuan functions: FAIL")

    if encoder_test and hunyuan_test:
        print("\n🎉 All tests passed! Text encoders are ready to use.")
    else:
        print("\n⚠️  Some tests failed. Please check the issues above.")

if __name__ == "__main__":
    main()
