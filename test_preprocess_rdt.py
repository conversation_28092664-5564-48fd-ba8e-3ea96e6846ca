#!/usr/bin/env python3
"""
测试 preprocess_rdt.py 的基本功能
"""

import sys
import os
import tempfile
import numpy as np
import pickle
import json
from PIL import Image
import torch

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dataset.preprocess_rdt import (
    extract_pre_latent_features,
    DummyPreLatentModel,
    VIDEO_DIFFUSION_AVAILABLE
)

def create_test_data(temp_dir, num_frames=10):
    """创建测试数据"""
    episode_dir = os.path.join(temp_dir, "episode0")
    os.makedirs(episode_dir, exist_ok=True)

    # 创建测试图像数据
    for i in range(num_frames):
        # 创建随机RGB图像数据
        rgb_data = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)

        # 创建observation数据
        data = {
            'observation': {
                'head_camera': {
                    'rgb': rgb_data
                }
            }
        }

        # 保存为pickle文件
        pkl_path = os.path.join(episode_dir, f"{i}.pkl")
        with open(pkl_path, 'wb') as f:
            pickle.dump(data, f)

    return episode_dir

def test_dummy_model():
    """测试dummy模型功能"""
    print("Testing dummy model...")

    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建测试数据
        episode_dir = create_test_data(temp_dir, num_frames=15)

        # 设置参数
        T = 9
        device = "cuda" if torch.cuda.is_available() else "cpu"
        instruction = "test instruction"

        # 创建模型和预处理
        model = DummyPreLatentModel(feature_dim=512)
        from torchvision import transforms
        preprocess = transforms.Compose([
            transforms.ToTensor(),
            transforms.Resize((224, 224)),
        ])

        # 运行特征提取
        try:
            features = extract_pre_latent_features(
                episode_dir, T, model, preprocess, device, instruction, episode_idx=0
            )

            print(f"✓ Dummy model test passed!")
            print(f"  Features shape: {features.shape}")
            print(f"  Expected windows: {15 - T + 1} = {features.shape[0]}")
            print(f"  Feature dimension: {features.shape[1]}")

            return True

        except Exception as e:
            print(f"✗ Dummy model test failed: {e}")
            return False

def test_video_diffusion_availability():
    """测试video diffusion模型的可用性"""
    print("Testing video diffusion availability...")

    if VIDEO_DIFFUSION_AVAILABLE:
        print("✓ Video diffusion models are available")
        try:
            from dataset.preprocess_rdt import (
                extract_pre_latent_features_with_video_diffusion,
                load_video_diffusion_models,
                process_latent_through_transformer
            )
            print("✓ Video diffusion functions imported successfully")
            print("✓ Guidance-based transformer processing function imported")
            return True
        except ImportError as e:
            print(f"✗ Failed to import video diffusion functions: {e}")
            return False
    else:
        print("⚠ Video diffusion models are not available (this is expected in some environments)")
        return True

def test_guidance_integration():
    """测试Guidance类集成"""
    print("Testing Guidance integration...")

    if not VIDEO_DIFFUSION_AVAILABLE:
        print("⚠ Skipping Guidance test - video diffusion models not available")
        return True

    try:
        from dataset.preprocess_rdt import process_latent_through_transformer
        import torch

        # 创建虚拟的latent slice
        latent_slice = torch.randn(1, 16, 4, 32, 48)  # (B, C, T, H, W)

        # 创建虚拟的guidance model (简化版本)
        class MockGuidanceModel:
            def __init__(self):
                self.dtype = torch.bfloat16
                self.transformer = MockTransformer()

            def load_features(self, moft=False):
                # 返回虚拟特征
                return {"block_19": torch.randn(3072, 4, 32, 48)}

        class MockTransformer:
            def __call__(self, **kwargs):
                # 简单返回None，实际中会有复杂的前向传播
                return None

        mock_guidance = MockGuidanceModel()
        device = "cpu"
        instruction = "test instruction"

        # 测试处理函数 (不使用text encoder)
        result = process_latent_through_transformer(
            latent_slice, mock_guidance, instruction, device
        )

        print(f"✓ Guidance integration test passed!")
        print(f"  Result shape: {result.shape}")
        return True

    except Exception as e:
        print(f"✗ Guidance integration test failed: {e}")
        return False


def test_text_encoder_integration():
    """测试Text Encoder集成"""
    print("Testing Text Encoder integration...")

    if not VIDEO_DIFFUSION_AVAILABLE:
        print("⚠ Skipping Text Encoder test - video diffusion models not available")
        return True

    try:
        from dataset.preprocess_rdt import (
            encode_instruction_with_hunyuan_text_encoder,
            create_dummy_text_embeddings
        )
        import torch

        # 测试虚拟text embeddings创建
        batch_size = 2
        device = "cpu"
        dtype = torch.float32

        encoder_hidden_states, attention_mask, pooled_projections = create_dummy_text_embeddings(
            batch_size, device, dtype
        )

        print(f"✓ Dummy text embeddings test passed!")
        print(f"  Encoder hidden states shape: {encoder_hidden_states.shape}")
        print(f"  Attention mask shape: {attention_mask.shape}")
        print(f"  Pooled projections shape: {pooled_projections.shape}")

        # 测试指令处理 (使用虚拟text encoder)
        instruction = "Pick up the red block and place it on the blue block"
        print(f"  Test instruction: '{instruction}'")

        return True

    except Exception as e:
        print(f"✗ Text Encoder integration test failed: {e}")
        return False

def main():
    """运行所有测试"""
    print("=" * 50)
    print("Testing preprocess_rdt.py functionality with Guidance and Text Encoder integration")
    print("=" * 50)

    tests_passed = 0
    total_tests = 4

    # 测试dummy模型
    if test_dummy_model():
        tests_passed += 1

    print()

    # 测试video diffusion可用性
    if test_video_diffusion_availability():
        tests_passed += 1

    print()

    # 测试Guidance集成
    if test_guidance_integration():
        tests_passed += 1

    print()

    # 测试Text Encoder集成
    if test_text_encoder_integration():
        tests_passed += 1

    print()
    print("=" * 50)
    print(f"Tests passed: {tests_passed}/{total_tests}")

    if tests_passed == total_tests:
        print("✓ All tests passed!")
        return 0
    else:
        print("✗ Some tests failed!")
        return 1

if __name__ == "__main__":
    exit(main())
