#!/usr/bin/env python3
"""
配置测试脚本 - 验证launch.json中的模型路径和配置是否正确
"""

import os
import sys
import json

def test_model_paths():
    """测试模型路径是否存在"""
    print("🔍 Testing model paths...")

    base_path = "/media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773"

    paths = {
        "VAE Model": f"{base_path}/vae/diffusion_pytorch_model.safetensors",
        "Transformer Model": "/media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7",
        "Text Encoder Base": base_path,
        "Text Encoder 1": f"{base_path}/text_encoder",
        "Text Encoder 2": f"{base_path}/text_encoder_2",
        "Tokenizer 1": f"{base_path}/tokenizer",
        "Tokenizer 2": f"{base_path}/tokenizer_2"
    }

    all_exist = True
    for name, path in paths.items():
        if os.path.exists(path):
            print(f"✅ {name}: {path}")
        else:
            print(f"❌ {name}: {path}")
            all_exist = False

    return all_exist

def test_instruction_files():
    """测试指令文件是否存在"""
    print("\n📝 Testing instruction files...")

    instruction_files = [
        "data/instructions/block_handover.json"
    ]

    all_exist = True
    for file_path in instruction_files:
        if os.path.exists(file_path):
            print(f"✅ Instruction file: {file_path}")
            # 验证JSON格式
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)
                    if 'instructions' in data:
                        print(f"   📊 Contains {len(data['instructions'])} instructions")
                    else:
                        print(f"   ⚠️  Missing 'instructions' key")
            except json.JSONDecodeError as e:
                print(f"   ❌ Invalid JSON format: {e}")
                all_exist = False
        else:
            print(f"❌ Instruction file: {file_path}")
            all_exist = False

    return all_exist

def test_data_directories():
    """测试数据目录是否存在"""
    print("\n📁 Testing data directories...")

    # 检查基本数据目录结构
    data_dirs = [
        "/media/jiayueru/Codes/RoboTwin/data",
        "/media/jiayueru/Codes/RoboTwin/data/block_handover_D435_pkl",
        "/media/jiayueru/Codes/RoboTwin/policy/RDT/processed_data"
    ]

    all_exist = True
    for dir_path in data_dirs:
        if os.path.exists(dir_path):
            print(f"✅ Data directory: {dir_path}")
            if os.path.isdir(dir_path):
                # 列出子目录
                try:
                    subdirs = [d for d in os.listdir(dir_path) if os.path.isdir(os.path.join(dir_path, d))]
                    if subdirs:
                        print(f"   📂 Contains {len(subdirs)} subdirectories")
                        if len(subdirs) <= 5:  # 只显示前5个
                            for subdir in subdirs[:5]:
                                print(f"      - {subdir}")
                    else:
                        print(f"   📂 Empty directory")
                except PermissionError:
                    print(f"   ⚠️  Permission denied")
        else:
            print(f"❌ Data directory: {dir_path}")
            all_exist = False

    return all_exist

def test_python_environment():
    """测试Python环境"""
    print("\n🐍 Testing Python environment...")

    print(f"✅ Python version: {sys.version}")
    print(f"✅ Python executable: {sys.executable}")

    # 测试关键依赖
    required_packages = [
        'torch',
        'numpy',
        'PIL',
        'h5py',
        'cv2'
    ]

    missing_packages = []
    for package in required_packages:
        try:
            if package == 'cv2':
                import cv2
                print(f"✅ {package}: {cv2.__version__}")
            elif package == 'PIL':
                from PIL import Image
                print(f"✅ {package}: Available")
            else:
                module = __import__(package)
                if hasattr(module, '__version__'):
                    print(f"✅ {package}: {module.__version__}")
                else:
                    print(f"✅ {package}: Available")
        except ImportError:
            print(f"❌ {package}: Not found")
            missing_packages.append(package)

    return len(missing_packages) == 0

def test_launch_json():
    """测试launch.json配置"""
    print("\n⚙️  Testing launch.json configuration...")

    launch_json_path = ".vscode/launch.json"
    if not os.path.exists(launch_json_path):
        print(f"❌ launch.json not found: {launch_json_path}")
        return False

    try:
        with open(launch_json_path, 'r') as f:
            config = json.load(f)

        configurations = config.get('configurations', [])
        dataset_configs = [c for c in configurations if 'Dataset' in c.get('name', '')]

        print(f"✅ Found {len(dataset_configs)} dataset configurations:")
        for config in dataset_configs:
            print(f"   - {config['name']}")

        return True

    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON in launch.json: {e}")
        return False

def main():
    """运行所有测试"""
    print("🧪 Configuration Test Suite")
    print("=" * 50)

    tests = [
        ("Model Paths", test_model_paths),
        ("Instruction Files", test_instruction_files),
        ("Data Directories", test_data_directories),
        ("Python Environment", test_python_environment),
        ("Launch Configuration", test_launch_json)
    ]

    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with error: {e}")
            results.append((test_name, False))

    # 总结
    print("\n" + "=" * 50)
    print("📊 Test Results Summary")
    print("=" * 50)

    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1

    print(f"\nOverall: {passed}/{len(results)} tests passed")

    if passed == len(results):
        print("\n🎉 All tests passed! Your configuration is ready to use.")
        print("\n💡 Next steps:")
        print("1. Open VSCode")
        print("2. Go to Run and Debug (Ctrl+Shift+D)")
        print("3. Select a dataset configuration")
        print("4. Click the green play button")
    else:
        print("\n⚠️  Some tests failed. Please check the issues above.")
        print("\n🔧 Common fixes:")
        print("- Ensure all model files are downloaded")
        print("- Check file paths in launch.json")
        print("- Verify data directories exist")
        print("- Install missing Python packages")

if __name__ == "__main__":
    main()
