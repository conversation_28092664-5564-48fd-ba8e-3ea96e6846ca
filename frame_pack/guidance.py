# Adapted from ditflow/motion_guidance.py (https://github.com/ditflow/ditflow)
import torch
import torch.nn as nn
from einops import rearrange
from frame_pack.uni_pc_fm import FlowMatchUniPC


def append_dims(x, target_dims):
    """Append dimensions to x until it has target_dims dimensions."""
    return x[(...,) + (None,) * (target_dims - x.ndim)]


def rescale_noise_cfg(noise_pred, noise_pred_text, guidance_rescale=0.0):
    """Rescale noise prediction based on classifier-free guidance."""
    std_text = noise_pred_text.std(dim=list(range(1, noise_pred_text.ndim)), keepdim=True)
    std_cfg = noise_pred.std(dim=list(range(1, noise_pred.ndim)), keepdim=True)
    # rescale the noise prediction to match the amplitude of the text noise prediction
    noise_pred_rescaled = noise_pred * (std_text / std_cfg)
    noise_pred = guidance_rescale * noise_pred_rescaled + (1 - guidance_rescale) * noise_pred
    return noise_pred


class ModuleWithGuidance(nn.Module):
    def __init__(self, module, h, w, p, block_name, num_frames):
        """ self.num_frames must be registered separately. """
        super().__init__()
        self.module = module
        self.attn = module.attn

        self.starting_shape = "(t h w) d"
        self.h = h
        self.w = w
        self.block_name = block_name
        self.num_frames = num_frames
        self.original_context_length = 13824
        self.inner_dim = 3072

    def forward(self, *args, **kwargs):
        out, text_out = self.module(*args, **kwargs) #[ 1, 17664, 3072]: 27664 = 9 * 32 * 48 + 3884
        self.saved_features = rearrange(
            out[:, -self.original_context_length:, :],
            "b (t h w) d -> b d t h w",
            t=self.num_frames,
            h=self.h,
            w=self.w,
        )

        return out, text_out
    
class Guidance(nn.Module):
    def __init__(self, transformer, config=None):
        super().__init__()
        self.transformer = transformer

        self.guidance_blocks = [19]  # Convert to list since block_idxs is iterated
        
        self.latent_height = 32
        self.latent_width = 48
        self.latent_num_frames = 9
        self.patch_size = 2
        self.dtype = torch.bfloat16
        self.config = config
        self.prop_motion = 0.04

        self.register_guidance(block_idxs=self.guidance_blocks)

    def fm_wrapper(self, transformer, t_scale=1000.0):
        """Wrap transformer for flow matching.
        
        Args:
            transformer: The transformer model to wrap
            t_scale: Time scaling factor
            
        Returns:
            A wrapped function for flow matching
        """
        def k_model(x, sigma, **extra_args):
            dtype = extra_args['dtype']
            cfg_scale = extra_args['cfg_scale']
            cfg_rescale = extra_args['cfg_rescale']
            concat_latent = extra_args['concat_latent']

            original_dtype = x.dtype
            sigma = sigma.float()

            x = x.to(dtype)
            timestep = (sigma * t_scale).to(dtype)

            if concat_latent is None:
                hidden_states = x
            else:
                hidden_states = torch.cat([x, concat_latent.to(x)], dim=1)

            pred_positive = transformer(hidden_states=hidden_states, timestep=timestep, return_dict=False, **extra_args['positive'])[0].float()

            if cfg_scale == 1.0:
                pred_negative = torch.zeros_like(pred_positive)
            else:
                pred_negative = transformer(hidden_states=hidden_states, timestep=timestep, return_dict=False, **extra_args['negative'])[0].float()

            pred_cfg = pred_negative + cfg_scale * (pred_positive - pred_negative)
            pred = rescale_noise_cfg(pred_cfg, pred_positive, guidance_rescale=cfg_rescale)

            x0 = x.float() - pred.float() * append_dims(sigma, x.ndim)

            return x0.to(dtype=original_dtype)
            
        return k_model

    def register_guidance(self, block_idxs, register_main=True, register_single=True):
        """Register guidance blocks to be able to save features in forward pass
        
        Args:
            block_idxs: List of block indices to register
            register_main: Whether to register blocks in transformer_blocks
            register_single: Whether to register blocks in single_transformer_blocks
        """

        if register_main and hasattr(self.transformer, 'transformer_blocks'):
            for out_i in block_idxs:
                if out_i < len(self.transformer.transformer_blocks):
                    block_name = f"block_{out_i}"
                    self.transformer.transformer_blocks[out_i] = ModuleWithGuidance(
                        self.transformer.transformer_blocks[out_i],
                        self.latent_height,
                        self.latent_width,
                        self.patch_size,
                        block_name=block_name,
                        num_frames=self.latent_num_frames,
                    )

        # if register_single and hasattr(self.transformer, 'single_transformer_blocks'):
        #     for out_i in block_idxs:
        #         if out_i < len(self.transformer.single_transformer_blocks):
        #             block_name = f"single_block_{out_i}"
        #             self.transformer.single_transformer_blocks[out_i] = ModuleWithGuidance(
        #                 self.transformer.single_transformer_blocks[out_i],
        #                 self.latent_height,
        #                 self.latent_width,
        #                 self.patch_size,
        #                 block_name=block_name,
        #                 num_frames=self.latent_num_frames,
        #             )
            
    @torch.no_grad()
    def load_features(self, moft=True):
        """Load saved features for motion video
        moft: Whether to compute motion channels for MOFT method
        """
        motion_features = {}
        motion_channels = {}

        for block_id in self.guidance_blocks:
            if block_id >= len(self.transformer.transformer_blocks):
                continue
                
            module = self.transformer.transformer_blocks[block_id]
            orig_features = module.saved_features[0].permute(1, 0, 2, 3)#[1, 3072, 9, 32, 48]
            motion_features[module.block_name] = orig_features
            
            if moft:
                orig_norm = orig_features - torch.mean(orig_features, axis=0)[None]
                num_frames, c, h, w = orig_norm.shape
                channels = orig_norm.permute(0,2,3,1).reshape(-1, c)
                _, _, Vt = torch.linalg.svd(channels.to(torch.float32), full_matrices=False)
                top_n = list(torch.argsort(torch.abs(Vt[0]), descending=True)[:int(self.prop_motion*c)])
                motion_channels[module.block_name] = top_n
        
        # 从single_transformer_blocks中提取特征
        # if hasattr(self.transformer, 'single_transformer_blocks'):
        #     for block_id in self.config.guidance_blocks:
        #         if block_id >= len(self.transformer.single_transformer_blocks):
        #             continue
                    
        #         block_name = f"single_block_{block_id}"
        #         module = self.transformer.single_transformer_blocks[block_id]
                
        #         if hasattr(module, 'saved_features'):
        #             orig_features = module.saved_features
        #             motion_features[block_name] = orig_features
                    
        #             if moft:
        #                 orig_norm = orig_features - torch.mean(orig_features, axis=0)[None]
        #                 num_frames, c, h, w = orig_norm.shape
        #                 channels = orig_norm.permute(0,2,3,1).reshape(-1, c)
        #                 _, _, Vt = torch.linalg.svd(channels.to(torch.float32), full_matrices=False)
        #                 top_n = list(torch.argsort(torch.abs(Vt[0]), descending=True)[:int(self.config.prop_motion*c)])
        #                 motion_channels[block_name] = top_n
                
        if moft:
            return motion_features, motion_channels
        return motion_features
    