# Video Diffusion Model Integration - Implementation Summary

## 概述

成功修改了 `dataset/preprocess_rdt.py` 文件，实现了使用 video diffusion model 的 VAE 架构和 transformer blocks 来处理整个 episode 的图像数据，并集成了 hunyuan video 的 text tokenizer 进行指令处理。

## 主要功能实现

### 1. Video Diffusion Model 集成

#### VAE 架构处理
- **整个 Episode 处理**: 将整个 episode 的所有图像作为 video 序列处理
- **Video Tensor 格式**: 转换为 `(B, C, T, H, W)` 格式
- **VAE 编码**: 通过 `AutoencoderKLCausal3D` 得到 latent representations
- **Scaling Factor**: 应用 VAE 的 scaling factor

#### Latent 分段切片
- **时间窗口切片**: 对 video latents 按时间维度进行分段切片
- **动态窗口大小**: 根据 VAE 的时间压缩比例计算窗口大小
- **批处理支持**: 支持不同的 chunk size 和 slice size

### 2. Transformer Block 特征提取

#### Guidance 类包装
- **Transformer 包装**: 使用 `Guidance` 类包装 `HunyuanVideoTransformer3DModelPacked`
- **特征提取**: 实现 `load_features` 方法从指定的 transformer blocks 提取 motion features
- **MOFT 支持**: 支持 Motion-Oriented Feature Transform (可选)

#### 特征处理流程
1. 通过 transformer 进行前向传播
2. 使用 `load_features` 方法提取指定 block 的特征
3. 对特征进行全局平均池化
4. 组合多个 block 的特征

### 3. Hunyuan Text Encoder 集成

#### 指令处理方式
- **模仿选中代码**: 完全模仿 `fpack_generate_video.py` 中的 text 处理方式
- **JSON 文件读取**: 使用类似于 `task_paths = [f"./data/instructions/{TASKNAME}.json"]` 的处理方式
- **多指令支持**: 支持每个 episode 使用不同的指令

#### Text Encoder 处理
- **双重编码器**: 使用 `load_text_encoder1` 和 `load_text_encoder2`
- **Tokenizer 集成**: 加载并使用对应的 tokenizers
- **编码流程**: 使用 `hunyuan.encode_prompt_conds` 进行编码
- **内存优化**: 将 embeddings 移到 CPU 以节省内存
- **Attention Mask**: 使用 `crop_or_pad_yield_mask` 处理 attention mask
- **回退机制**: 如果 text encoder 不可用，回退到虚拟 embeddings

#### Text Embeddings 保存
- **自动保存**: 为每个 episode 自动保存 text embeddings
- **HDF5 格式**: 保存为 `episode_{i}_text_embeddings.hdf5`
- **完整数据**: 保存 llama_vec, attention_mask, clip_l_pooler 和原始指令

## 新增函数

### 核心函数
1. **`extract_pre_latent_features_with_video_diffusion`**: 主要的 video diffusion 处理函数
2. **`process_latent_through_transformer`**: 使用 Guidance 类的 transformer 处理函数
3. **`encode_instruction_with_hunyuan_text_encoder`**: 模仿 fpack_generate_video.py 的 text 编码方式
4. **`create_dummy_text_embeddings`**: 创建虚拟 text embeddings
5. **`load_video_diffusion_models`**: 加载 VAE、transformer、text encoder 和 tokenizer 模型
6. **`save_text_embeddings_for_episode`**: 为每个 episode 保存 text embeddings

### 辅助功能
- 错误处理和回退机制
- 设备和数据类型管理
- 批处理和内存优化

## 使用方式

### 基本用法 (Dummy 模型)
```bash
python dataset/preprocess_rdt.py task_name camera_type num_episodes --T 9
```

### Video Diffusion 模式 (不使用 text encoder)
```bash
python dataset/preprocess_rdt.py task_name camera_type num_episodes \
    --T 9 \
    --use_video_diffusion \
    --vae_path /path/to/vae/model \
    --transformer_path /path/to/transformer/model \
    --chunk_size 8 --latent_slice_size 16
```

### Video Diffusion 模式 + Hunyuan Text Encoder
```bash
python dataset/preprocess_rdt.py task_name camera_type num_episodes \
    --T 9 \
    --use_video_diffusion \
    --vae_path /path/to/vae/model \
    --transformer_path /path/to/transformer/model \
    --text_encoder_path /path/to/text/encoder \
    --chunk_size 8 --latent_slice_size 16
```

## 技术特点

### 1. 模块化设计
- 清晰的函数分离
- 可选的 text encoder 支持
- 灵活的配置参数

### 2. 错误处理
- 导入错误处理
- 模型加载失败回退
- 运行时异常捕获

### 3. 性能优化
- 批处理支持
- 内存管理
- GPU 加速支持

### 4. 兼容性
- 向后兼容原始 dummy 模型
- 可选的高级功能
- 多种运行模式

## 输出格式

### Pre-latent 特征
- **文件格式**: HDF5 格式 (`episode_{i}_prelatent.hdf5`)
- **数据集名**: `cam_high_pre_features`
- **数据形状**: `(num_windows, feature_dim)`
- **特征维度**: 根据提取的 transformer block 特征确定

### Text Embeddings (如果启用 text encoder)
- **文件格式**: HDF5 格式 (`episode_{i}_text_embeddings.hdf5`)
- **数据集**:
  - `llama_vec`: LLM 编码的文本特征 `(1, 512, 4096)`
  - `llama_attention_mask`: 注意力掩码 `(1, 512)`
  - `clip_l_pooler`: CLIP 编码的池化特征 `(1, 768)`
  - `instruction`: 原始指令文本 (UTF-8 编码)

## 测试支持

创建了完整的测试套件 (`test_preprocess_rdt.py`):
1. Dummy 模型基本功能测试
2. Video diffusion 模型可用性测试
3. Guidance 类集成测试
4. Text Encoder 集成测试

## 文档支持

- **README**: 详细的使用说明和技术细节
- **代码注释**: 完整的函数和参数说明
- **示例**: 多种使用场景的示例

## 文档和示例

- **README**: 详细的使用说明和技术细节
- **代码注释**: 完整的函数和参数说明
- **示例脚本**: `load_text_embeddings_example.py` 展示如何加载和使用保存的 embeddings
- **使用示例**: 多种使用场景的命令行示例

## 总结

成功实现了完整的 video diffusion model 集成，完全模仿选中代码的 text 处理方式：

### ✅ 核心功能实现
- **整个 episode 处理**: 图像通过 video diffusion model 的 VAE 架构
- **Latent 分段切片**: 对 latent 进行时间维度的分段切片
- **Guidance 包装**: 通过包装在 Guidance 类中的 diffusion model transformer blocks 处理
- **Text 处理**: 完全模仿 `fpack_generate_video.py` 的 text 处理方式

### ✅ Text Encoder 集成
- **双重编码器**: 使用 `load_text_encoder1` 和 `load_text_encoder2`
- **编码流程**: 使用 `hunyuan.encode_prompt_conds` 进行编码
- **内存优化**: 将 embeddings 移到 CPU 以节省内存
- **Attention Mask**: 使用 `crop_or_pad_yield_mask` 处理

### ✅ Embeddings 保存
- **自动保存**: 为每个 episode 自动保存 text embeddings
- **完整数据**: 保存 llama_vec, attention_mask, clip_l_pooler 和原始指令
- **HDF5 格式**: 便于后续加载和使用

### ✅ 其他特性
- **完整的错误处理和回退机制**
- **详细的文档和测试支持**
- **示例脚本和使用指南**

代码现在完全支持你描述的工作流程，并且完全模仿了选中代码的 text 处理方式，同时保存了所有的 embeddings 以供后续使用。
